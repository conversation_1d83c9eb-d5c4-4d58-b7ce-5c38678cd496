1. WebUI无法启动
如果WebUI无法启动，可以先运行 "![[安装WebUI依赖.bat" 来进行快速修复
如果无法修复，进入
.\modules\HMMLDemon
在这个路径下运行cmd，执行以下命令
..\..\runtime\nodejs\pnpm.cmd install --registry https://registry.npmmirror.com


2. 出现麦麦根目录没有设置/麦麦适配器根目录没有设置
点击右上角设置 -> 重置设置/重新设置 打开启动设置面版

3. 一键包无法启动麦麦，提示”no module named XXX“
修改启动命令
麦麦主程序修改为：..\..\runtime\python31211\bin\python.exe bot.py
麦麦适配器修改为：..\..\runtime\python31211\bin\python.exe main.py


如果你试过了所有的方法，还是装不上WebUI的依赖
你可以进入runtime文件夹，找到一个 webui依赖.msi 的安装包，双击他安装nodejs运行环境
然后打开一个cmd窗口，依次输入以下命令：

npm config set registry https://registry.npmmirror.com
npm install -g pnpm

然后重新运行安装WebUI依赖.bat