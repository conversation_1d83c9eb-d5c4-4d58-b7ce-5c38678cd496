{"manifest_version": 1, "name": "插件和组件管理 (Plugin and Component Management)", "version": "1.0.0", "description": "通过系统API管理插件和组件的生命周期，包括加载、卸载、启用和禁用等操作。", "author": {"name": "MaiBot团队", "url": "https://github.com/MaiM-with-u"}, "license": "GPL-v3.0-or-later", "host_application": {"min_version": "0.9.1"}, "homepage_url": "https://github.com/MaiM-with-u/maibot", "repository_url": "https://github.com/MaiM-with-u/maibot", "keywords": ["plugins", "components", "management", "built-in"], "categories": ["Core System", "Plugin Management"], "default_locale": "zh-CN", "locales_path": "_locales", "plugin_info": {"is_built_in": true, "plugin_type": "plugin_management", "components": [{"type": "command", "name": "plugin_management", "description": "管理插件和组件的生命周期，包括加载、卸载、启用和禁用等操作。"}]}}