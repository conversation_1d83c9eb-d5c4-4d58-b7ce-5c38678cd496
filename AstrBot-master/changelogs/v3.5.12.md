# What's Changed

1. 新增：支持 MCP 的 Streamable HTTP 传输方式。详见 [#1637](https://github.com/Soulter/AstrBot/issues/1637)
2. 新增：支持 MCP 的 SSE 传输方式的自定义请求头。详见 [#1659](https://github.com/Soulter/AstrBot/issues/1659)
3. 优化：将 /llm 和 /model 和 /provider 指令设置为管理员指令
4. 修复：修复插件的 priority 部分失效的问题
5. 修复：修复 QQ 下合并转发消息内无法发送文件等问题，尽可能修复了各种文件、语音、视频、图片无法发送的问题
6. 优化：Telegram 支持长消息分段发送，优化消息编辑的逻辑
7. 优化：WebUI 强制默认修改密码
8. 优化：移除了 vpet
9. 新增：插件接口：支持动态路由注册
10. 优化：CLI 模式下的插件下载
11. 新增：WeChatPadPro 对接获取联系人接口
12. 新增：T2I、语音、视频支持文件服务
13. 优化：硅基流动下某些工具调用返回的 argument 格式适配
14. 优化：在使用 /llm 指令关闭后重启 AstrBot 后，模型提供商未被加载
15. 新增：新增基于 FAISS + SQLite 的向量存储接口
16. 新增：Alkaid Page
