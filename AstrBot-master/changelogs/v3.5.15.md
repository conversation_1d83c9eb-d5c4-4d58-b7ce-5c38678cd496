# What's Changed

1. 修复：如果设置了 GitHub 加速地址，更新插件会报错
2. 修复：部分场景下，`只@触发等待` 配置项功能无效的问题
3. 新增：增加 `只@触发等待时是否回复` 配置项
4. 新增：**支持模型提供商使用时会话隔离(需要手动开启配置项：提供商会话隔离)**
5. 新增：Google Gemini 提供商支持 URL 上下文功能
6. 新增：优化 WebChat 的 UI 显示，WebChat 支持修改标题和自动生成标题，支持 WebChatBox
7. 新增：支持可配置是否忽略 @ 全体成员
8. 优化：WebUI 顶栏移动端显示
9. 优化：插件/AstrBot 配置项完整性检查的同时也保证**配置项相对顺序一致性**
10. 优化：perf: 分段回复时，仅在输出的第一句话带上回复/引用
11. 修复: Windows 下部署项目时可能出现的 UnicodeDecodeError。
