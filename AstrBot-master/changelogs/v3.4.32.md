# What's Changed


1. ✨ 新增: Add a draggable iframe for tutorial links and enhance platform configuration UI
2. ✨ 新增: 集成 astrbot_plugin_telegram/企业微信 至 astrbot
3. ✨ 新增: openai_source 支持传入任何自定义参数以适配 Ollama 和 FastGPT 等 provider
4. ✨ 新增: Telegram 适配器中支持 @ 唤醒
5. ✨ 新增: 添加面板下载按钮置灰 by @Fridemn
6. ✨ 新增: 添加 SenseVoice 语音转文本（STT）服务 by @diudiu62
7. ⚡ 优化: Increase forward threshold from 200 to 1500 in default configuration
8. ⚡ 优化: 添加控制台关闭自动滚动按钮 by @Fridemn
9.  🐛 修复: 修复前端面板部分页面刷新后的 404 错误
10. 🐛 修复: 修复某些情况下热重载 服务提供商 时可能没有正确应用的问题
11. 🐛 修复: 修复 Telegram 适配器中未处理 base64 的问题 @Raven95676
12. 🐛 修复: 修复 Dify 主动回复报错的问题 #616