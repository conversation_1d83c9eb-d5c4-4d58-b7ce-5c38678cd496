# What's Changed

1. ✨ 新增: gemini source 初步支持对 API Key 进行负载均衡请求 #534
2. ✨ 新增: 开启对话隔离的群聊以及私聊下，非 op 可以可以使用 /del 和 /reset #519
3. ✨ 新增: 事件钩子支持 yield 方式发送消息
4. ⚡ 优化: 查询模型列表时，可以显示当前使用的模型名称 #523
5. ⚡ 优化: 更换为预编译指令的方式处理指令组指令
6. 🐛 修复: resolve KeyError when current conversation is not in paginated list
7. 🐛 修复: 修复指令组的情况下，Permission Filter 对子指令失效的问题
8.  🐛 修复: 🐛 fix: 修复 reminder rm失败 #529
9.  🐛 修复: 🐛 fix: reminder 时区问题 #529
10. 🐛 修复: 修复 Dify 下无法主动回复的问题 #494
11. 🐛 修复: 添加代码执行器 Docker 宿主机绝对路径配置及相关功能以修复 Docker 下无法使用代码执行器的问题 #525
12. 🐛 修复: gewechat 微信群聊情况下可能导致 unknown 的问题 #537