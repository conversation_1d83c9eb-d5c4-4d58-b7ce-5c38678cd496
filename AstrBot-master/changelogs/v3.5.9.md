# What's Changed

1. 重构: 采用更好的方式将文件上传到 NapCat 协议端，无需映射路径。**（需要前往 配置->其他配置 中配置`对外可达的回调接口地址`）** @Soulter @anka-afk
2. 修复: 单独发送文件时被认为是空消息导致文件无法发送的问题 @Soulter
3. 修复: Lagrange 下合并转发消息失败的问题 @Soulter
4. 修复: CLI 模式下路径问题导致 WebUI 和 MCP Server 无法加载的问题 @Soulter
5. 修复: 设置 Gemini 的 thinking_budget 前，先检查是否存在 @Raven95676
6. 修复: 修复企业微信和微信公众平台下无法应用 api_base_url 的问题 @Soulter
7. 优化: 分离 plugin 指令为指令组，优化 plugin 指令权限控制 @Soulter
8. 优化: WebUI 更直观的模型提供商选择 @Soulter
9. 优化: AstrBot 的重启逻辑 @Anchor
10. 新增: CLI 支持部分配置文件项的设定、支持插件管理和检测到插件文件变化时自动热重载 @Raven95676
11. 新增: 现已支持 Azure TTS @NanoRocky