# What's Changed

> 重构了大模型请求部分，如果发现此部分使用时有问题请提交 issue

1. 修复: 安装插件按钮被删除、无法自定义安装插件
2. 修复: 环境变量中的代理地址无法生效
1. 修复: randomize jwt secret
2. 修复: 在 Node 消息段发送简单文本信息的问题
1. 修复: QQ 官方机器人适配器使用 SessionController(会话控制)功能时机器人回复消息无法发送到聊天平台
4. 修复: Discord 适配器无法优雅重载
1. 修复: Telegram 适配器无法主动回复
1. 修复: 仪表盘的『插件配置』中不显示代码编辑器
3. 新增: Gemini TTS API
1. 新增: 允许 html_render 方法传入 Playwright.screenshot 配置参数
1. 优化: 修复 CommandFilter 支持对布尔类型进行解析
4. 新增: WechatPadPro 发送 TTS 时 添加对 MP3 格式音频支持
1. 重构: 将大模型请求部分抽象成 AgentRunner，提高可读性和可扩展性，工具调用结果支持持久化保存到数据库，完善 Agent 的多轮工具调用能力。
1. 移除: LLMTuner 模型提供商适配器。请使用 Ollama 来加载微调模型