export type ThemeTypes = {
  name: string;
  dark: boolean;
  variables?: object;
  colors: {
    primary?: string;
    secondary?: string;
    info?: string;
    success?: string;
    accent?: string;
    warning?: string;
    error?: string;
    lightprimary?: string;
    lightsecondary?: string;
    lightsuccess?: string;
    lighterror?: string;
    lightwarning?: string;
    darkprimary?: string;
    darksecondary?: string;
    primaryText?: string;
    secondaryText?: string;
    borderLight?: string;
    border?: string;
    inputBorder?: string;
    containerBg?: string;
    surface?: string;
    background?: string;
    overlay?: string;
    'on-surface-variant'?: string;
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    gray100?: string;
    primary200?: string;
    secondary200?: string;
    codeBg?: string;
    preBg?: string;
    code?: string;
    chatMessageBubble?: string;
    mcpCardBg?: string;
  };
};
